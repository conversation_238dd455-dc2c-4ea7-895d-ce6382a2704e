// Core entity interfaces for the Ayura Admin Panel

export interface Doctor {
  _id: string;
  name: string;
  email: string;
  phone: string;
  specialization: string;
  experience: number;
  consultationFee: number;
  rating?: number;
  totalConsultations: number;
  isActive: boolean;
  languages: string[];
  availability: {
    monday: { start: string; end: string; available: boolean };
    tuesday: { start: string; end: string; available: boolean };
    wednesday: { start: string; end: string; available: boolean };
    thursday: { start: string; end: string; available: boolean };
    friday: { start: string; end: string; available: boolean };
    saturday: { start: string; end: string; available: boolean };
    sunday: { start: string; end: string; available: boolean };
  };
  qualifications: string[];
  bio: string;
  profileImage?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Patient {
  _id: string;
  name: string;
  email: string;
  phone: string;
  age: number;
  gender: 'Male' | 'Female' | 'Other';
  address: string;
  dateOfBirth: string;
  isActive: boolean;
  emergencyContact: {
    name: string;
    phone: string;
    relationship: string;
  };
  medicalHistory: {
    bloodGroup?: string;
    allergies: string[];
    currentMedications: string[];
    conditions: string[];
    notes?: string;
  };
  profileImage?: string;
  totalConsultations: number;
  lastVisit?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Consultation {
  _id: string;
  patient: {
    _id: string;
    name: string;
    email: string;
    phone: string;
  };
  doctor: {
    _id: string;
    name: string;
    specialization: string;
  };
  appointmentDate: string;
  appointmentTime: string;
  type: string;
  mode: 'online' | 'offline';
  status: 'Scheduled' | 'In Progress' | 'Completed' | 'Cancelled' | 'No Show';
  fee: {
    amount: number;
    currency: string;
  };
  symptoms?: string;
  diagnosis?: string;
  prescription?: string;
  notes?: string;
  followUpDate?: string;
  createdAt: string;
  updatedAt: string;
}

// Form interfaces for editing
export interface DoctorFormData {
  name: string;
  email: string;
  phone: string;
  specialization: string;
  experience: number;
  consultationFee: number;
  languages: string[];
  availability: Doctor['availability'];
  qualifications: string[];
  bio: string;
  isActive: boolean;
  profileImage?: string;
}

export interface PatientFormData {
  name: string;
  email: string;
  phone: string;
  age: number;
  gender: 'Male' | 'Female' | 'Other';
  address: string;
  dateOfBirth: string;
  isActive: boolean;
  emergencyContact: {
    name: string;
    phone: string;
    relationship: string;
  };
  medicalHistory: {
    bloodGroup?: string;
    allergies: string[];
    currentMedications: string[];
    conditions: string[];
    notes?: string;
  };
  profileImage?: string;
}

export interface ConsultationFormData {
  patientId: string;
  doctorId: string;
  appointmentDate: string;
  appointmentTime: string;
  type: string;
  mode: 'In-person' | 'Video Call' | 'Phone Call';
  status: 'Scheduled' | 'In Progress' | 'Completed' | 'Cancelled' | 'No Show';
  fee: {
    amount: number;
    currency: string;
  };
  symptoms?: string;
  diagnosis?: string;
  prescription?: string;
  notes?: string;
  followUpDate?: string;
}

// Slot interfaces
export interface Slot {
  _id: string;
  doctor: {
    _id: string;
    name: string;
    specialization: string;
  };
  date: string;
  startTime: string;
  endTime: string;
  duration: number; // in minutes
  status: 'available' | 'booked' | 'blocked' | 'cancelled';
  consultationType: string;
  fee: {
    amount: number;
    currency: string;
  };
  patient?: {
    _id: string;
    name: string;
    phone: string;
  };
  consultation?: string; // consultation ID if booked
  isRecurring: boolean;
  recurringPattern?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    interval: number;
    endDate?: string;
  };
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SlotFormData {
  doctorId: string;
  date: string;
  startTime: string;
  endTime: string;
  duration: number;
  consultationType: string;
  fee: {
    amount: number;
    currency: string;
  };
  isRecurring: boolean;
  recurringPattern?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    interval: number;
    endDate?: string;
  };
  notes?: string;
}

export interface BulkSlotData {
  doctorId: string;
  dates: string[];
  timeSlots: string[];
  duration: number;
  consultationType: string;
  fee: {
    amount: number;
    currency: string;
  };
  isRecurring: boolean;
  recurringWeeks?: number;
}

// API Response interfaces
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// Validation interfaces
export interface ValidationError {
  field: string;
  message: string;
}

export interface FormErrors {
  [key: string]: string;
}

// Filter and search interfaces
export interface ConsultationFilters {
  search: string;
  status: string;
  doctorId: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface DoctorFilters {
  search: string;
  specialization: string;
  isActive?: boolean;
}

export interface PatientFilters {
  search: string;
  status: string;
  ageFrom?: number;
  ageTo?: number;
}

export interface SlotFilters {
  search: string;
  doctorId: string;
  status: string;
  consultationType?: string;
}
