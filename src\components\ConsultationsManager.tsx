import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "./ui/card";
import { Button } from "./ui/button";
import { Badge } from "./ui/badge";
import { Input } from "./ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "./ui/dialog";
import { Calendar, Search, Filter, Eye, Edit, Trash2, Video, Phone, Plus, RefreshCw } from "lucide-react";
import { consultationsAPI, doctorsAPI, patientsAPI } from '../utils/api';
import { toast } from 'sonner';
import { ConsultationForm } from './forms/ConsultationForm';
import { Consultation, ConsultationFormData, Doctor } from '../types/models';

export function ConsultationsManager() {
  const [consultations, setConsultations] = useState<Consultation[]>([]);
  const [doctors, setDoctors] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [doctorFilter, setDoctorFilter] = useState('all');
  const [refreshing, setRefreshing] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedConsultation, setSelectedConsultation] = useState<Consultation | null>(null);
  const [formLoading, setFormLoading] = useState(false);

  // Fetch consultations and doctors on component mount
  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [consultationsResponse, doctorsResponse] = await Promise.all([
        consultationsAPI.getAll(),
        doctorsAPI.getAll()
      ]);

      if (consultationsResponse.success) {
        setConsultations(consultationsResponse.data);
      }

      if (doctorsResponse.success) {
        setDoctors(doctorsResponse.data);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to load consultations');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
    toast.success('Data refreshed successfully');
  };

  // Filter consultations based on search and filters
  const filteredConsultations = consultations.filter(consultation => {
    const matchesSearch = searchTerm === '' ||
      consultation.patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      consultation.doctor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      consultation.type.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || consultation.status.toLowerCase() === statusFilter.toLowerCase();
    const matchesDoctor = doctorFilter === 'all' || consultation.doctor._id === doctorFilter;

    return matchesSearch && matchesStatus && matchesDoctor;
  });

  // Handle consultation actions
  const handleViewConsultation = (consultation: Consultation) => {
    // TODO: Open consultation details modal
    toast.info(`Viewing consultation ${consultation._id}`);
  };

  const handleEditConsultation = (consultation: Consultation) => {
    setSelectedConsultation(consultation);
    setIsEditDialogOpen(true);
  };

  const handleDeleteConsultation = async (consultation: Consultation) => {
    if (window.confirm('Are you sure you want to delete this consultation?')) {
      try {
        await consultationsAPI.delete(consultation._id);
        toast.success('Consultation deleted successfully');
        fetchData(); // Refresh the list
      } catch (error) {
        console.error('Error deleting consultation:', error);
        toast.error('Failed to delete consultation');
      }
    }
  };

  const handleStatusChange = async (consultationId: string, newStatus: string) => {
    try {
      await consultationsAPI.updateStatus(consultationId, newStatus);
      toast.success('Status updated successfully');
      fetchData(); // Refresh the list
    } catch (error) {
      console.error('Error updating status:', error);
      toast.error('Failed to update status');
    }
  };

  const handleCreateConsultation = async (data: ConsultationFormData) => {
    try {
      setFormLoading(true);
      await consultationsAPI.create(data);
      toast.success('Consultation created successfully');
      setIsCreateDialogOpen(false);
      fetchData();
    } catch (error) {
      console.error('Error creating consultation:', error);
      toast.error('Failed to create consultation');
    } finally {
      setFormLoading(false);
    }
  };

  const handleUpdateConsultation = async (data: ConsultationFormData) => {
    if (!selectedConsultation) return;

    try {
      setFormLoading(true);
      await consultationsAPI.update(selectedConsultation._id, data);
      toast.success('Consultation updated successfully');
      setIsEditDialogOpen(false);
      setSelectedConsultation(null);
      fetchData();
    } catch (error) {
      console.error('Error updating consultation:', error);
      toast.error('Failed to update consultation');
    } finally {
      setFormLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: { [key: string]: "default" | "secondary" | "destructive" | "outline" } = {
      'Completed': "default",
      'In Progress': "secondary",
      'Scheduled': "outline",
      'Cancelled': "destructive",
      'No Show': "destructive"
    };
    const variant = variants[status] || "outline";
    return <Badge variant={variant}>{status}</Badge>;
  };

  const getModeIcon = (mode: string) => {
    return mode === "video" ? <Video className="h-4 w-4" /> : <Phone className="h-4 w-4" />;
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold">Consultations</h1>
          <p className="text-muted-foreground">Manage all consultation bookings</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleRefresh} variant="outline" disabled={refreshing}>
            <RefreshCw className={`mr-2 h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Schedule New
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-7xl max-w-[90vw] max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Create New Consultation</DialogTitle>
              </DialogHeader>
              <ConsultationForm
                onSubmit={handleCreateConsultation}
                onCancel={() => setIsCreateDialogOpen(false)}
                isLoading={formLoading}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search patients, doctors..."
                  className="pl-9"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="scheduled">Scheduled</SelectItem>
                <SelectItem value="in progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
                <SelectItem value="no show">No Show</SelectItem>
              </SelectContent>
            </Select>
            <Select value={doctorFilter} onValueChange={setDoctorFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Doctor" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Doctors</SelectItem>
                {doctors.map((doctor) => (
                  <SelectItem key={doctor._id} value={doctor._id}>
                    {doctor.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              More Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Consultations Table */}
      <Card>
        <CardHeader>
          <CardTitle>
            All Consultations ({filteredConsultations.length})
            {loading && <span className="text-sm font-normal text-muted-foreground ml-2">Loading...</span>}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading consultations...</span>
            </div>
          ) : filteredConsultations.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No consultations found matching your criteria.
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-2">ID</th>
                    <th className="text-left py-3 px-2">Patient</th>
                    <th className="text-left py-3 px-2">Doctor</th>
                    <th className="text-left py-3 px-2">Date & Time</th>
                    <th className="text-left py-3 px-2">Type</th>
                    <th className="text-left py-3 px-2">Mode</th>
                    <th className="text-left py-3 px-2">Fee</th>
                    <th className="text-left py-3 px-2">Status</th>
                    <th className="text-left py-3 px-2">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredConsultations.map((consultation) => (
                    <tr key={consultation._id} className="border-b hover:bg-muted/50">
                      <td className="py-3 px-2 font-mono text-sm">
                        {consultation._id.slice(-6).toUpperCase()}
                      </td>
                      <td className="py-3 px-2">
                        <div>
                          <div className="font-medium">{consultation.patient.name}</div>
                          <div className="text-sm text-muted-foreground">{consultation.patient.phone}</div>
                        </div>
                      </td>
                      <td className="py-3 px-2">
                        <div>
                          <div className="font-medium">{consultation.doctor.name}</div>
                          <div className="text-sm text-muted-foreground">{consultation.doctor.specialization}</div>
                        </div>
                      </td>
                      <td className="py-3 px-2">
                        <div>
                          <div>{new Date(consultation.appointmentDate).toLocaleDateString()}</div>
                          <div className="text-sm text-muted-foreground">{consultation.appointmentTime}</div>
                        </div>
                      </td>
                      <td className="py-3 px-2">{consultation.type}</td>
                      <td className="py-3 px-2">
                        <div className="flex items-center space-x-2">
                          {getModeIcon(consultation.mode)}
                          <span className="capitalize">{consultation.mode}</span>
                        </div>
                      </td>
                      <td className="py-3 px-2">
                        {consultation.fee ? `₹${consultation.fee.amount}` : 'N/A'}
                      </td>
                      <td className="py-3 px-2">{getStatusBadge(consultation.status)}</td>
                      <td className="py-3 px-2">
                        <div className="flex space-x-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleViewConsultation(consultation)}
                            title="View Details"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleEditConsultation(consultation)}
                            title="Edit Consultation"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleDeleteConsultation(consultation)}
                            title="Delete Consultation"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Consultation Dialog */}
      {selectedConsultation && (
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Consultation</DialogTitle>
            </DialogHeader>
            <ConsultationForm
              consultation={selectedConsultation}
              onSubmit={handleUpdateConsultation}
              onCancel={() => {
                setIsEditDialogOpen(false);
                setSelectedConsultation(null);
              }}
              isLoading={formLoading}
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}