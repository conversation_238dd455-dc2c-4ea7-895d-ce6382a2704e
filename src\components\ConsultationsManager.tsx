import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "./ui/card";
import { Button } from "./ui/button";
import { Badge } from "./ui/badge";
import { Input } from "./ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "./ui/dialog";
import { Popover, PopoverContent, PopoverTrigger } from "./ui/popover";
import { Calendar, Search, Filter, Eye, Edit, Trash2, Video, Phone, Plus, RefreshCw, AlertTriangle } from "lucide-react";
import { consultationsAPI, doctorsAPI, patientsAPI } from '../utils/api';
import { toast } from 'sonner';
import { ConsultationForm } from './forms/ConsultationForm';
import { Consultation, ConsultationFormData, Doctor } from '../types/models';

export function ConsultationsManager() {
  const [consultations, setConsultations] = useState<Consultation[]>([]);
  const [doctors, setDoctors] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [doctorFilter, setDoctorFilter] = useState('all');
  const [refreshing, setRefreshing] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [deletePopoverOpen, setDeletePopoverOpen] = useState<string | null>(null);
  const [selectedConsultation, setSelectedConsultation] = useState<Consultation | null>(null);
  const [formLoading, setFormLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // Fetch consultations and doctors on component mount
  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [consultationsResponse, doctorsResponse] = await Promise.all([
        consultationsAPI.getAll(),
        doctorsAPI.getAll()
      ]);

      if (consultationsResponse.success) {
        setConsultations(consultationsResponse.data);
      }

      if (doctorsResponse.success) {
        setDoctors(doctorsResponse.data);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to load consultations');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
    toast.success('Data refreshed successfully');
  };

  // Filter consultations based on search and filters
  const filteredConsultations = consultations.filter(consultation => {
    const matchesSearch = searchTerm === '' ||
      consultation.patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      consultation.doctor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      consultation.type.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || consultation.status.toLowerCase() === statusFilter.toLowerCase();
    const matchesDoctor = doctorFilter === 'all' || consultation.doctor._id === doctorFilter;

    return matchesSearch && matchesStatus && matchesDoctor;
  });

  // Handle consultation actions
  const handleViewConsultation = (consultation: Consultation) => {
    console.log('Viewing consultation:', consultation);
    setSelectedConsultation(consultation);
    setIsViewDialogOpen(true);
  };

  const handleEditConsultation = (consultation: Consultation) => {
    console.log('Editing consultation:', consultation);
    setSelectedConsultation(consultation);
    setIsEditDialogOpen(true);
  };

  const handleDeleteConsultation = async (consultationId: string) => {
    console.log('Delete consultation called with ID:', consultationId, typeof consultationId);
    try {
      setDeleteLoading(true);
      const response = await consultationsAPI.delete(consultationId);
      console.log('Delete response:', response);

      // The API returns { success: true, message: '...', data: {...} }
      // If the request succeeds, the response will have success: true
      toast.success(response.message || 'Consultation deleted successfully');
      setDeletePopoverOpen(null); // Close the popover
      fetchData(); // Refresh the list
    } catch (error: any) {
      console.error('Error deleting consultation:', error);
      const errorMessage = error?.message || 'Failed to delete consultation';
      toast.error(errorMessage);
    } finally {
      setDeleteLoading(false);
    }
  };

  const handleStatusChange = async (consultationId: string, newStatus: string) => {
    try {
      await consultationsAPI.updateStatus(consultationId, newStatus);
      toast.success('Status updated successfully');
      fetchData(); // Refresh the list
    } catch (error) {
      console.error('Error updating status:', error);
      toast.error('Failed to update status');
    }
  };

  const handleCreateConsultation = async (data: ConsultationFormData) => {
    try {
      setFormLoading(true);

      // Transform the data to match backend expectations
      const consultationData = {
        patient: data.patientId,
        doctor: data.doctorId,
        appointmentDate: data.appointmentDate,
        appointmentTime: data.appointmentTime,
        type: data.type,
        mode: data.mode,
        status: data.status,
        fee: data.fee,
        // Transform symptoms from string to array format expected by backend
        symptoms: data.symptoms && data.symptoms.trim() ? [{
          symptom: data.symptoms.trim(),
          severity: 'Mild',
          duration: ''
        }] : undefined,
        // Transform diagnosis from string to object format expected by backend
        diagnosis: data.diagnosis && data.diagnosis.trim() ? {
          primary: data.diagnosis.trim(),
          secondary: [],
          notes: ''
        } : undefined,
        // Transform prescription to treatment.medications if provided
        treatment: data.prescription && data.prescription.trim() ? {
          medications: [{
            name: data.prescription.trim(),
            dosage: '',
            frequency: '',
            duration: '',
            instructions: ''
          }],
          therapies: [],
          lifestyle: []
        } : undefined,
        notes: data.notes,
        followUpDate: data.followUpDate
      };

      await consultationsAPI.create(consultationData);
      toast.success('Consultation created successfully');
      setIsCreateDialogOpen(false);
      fetchData();
    } catch (error) {
      console.error('Error creating consultation:', error);
      toast.error('Failed to create consultation');
    } finally {
      setFormLoading(false);
    }
  };

  const handleUpdateConsultation = async (data: ConsultationFormData) => {
    if (!selectedConsultation) return;

    try {
      setFormLoading(true);

      // Transform the data to match backend expectations
      const consultationData = {
        patient: data.patientId,
        doctor: data.doctorId,
        appointmentDate: data.appointmentDate,
        appointmentTime: data.appointmentTime,
        type: data.type,
        mode: data.mode,
        status: data.status,
        fee: data.fee,
        // Transform symptoms from string to array format expected by backend
        symptoms: data.symptoms && data.symptoms.trim() ? [{
          symptom: data.symptoms.trim(),
          severity: 'Mild',
          duration: ''
        }] : undefined,
        // Transform diagnosis from string to object format expected by backend
        diagnosis: data.diagnosis && data.diagnosis.trim() ? {
          primary: data.diagnosis.trim(),
          secondary: [],
          notes: ''
        } : undefined,
        // Transform prescription to treatment.medications if provided
        treatment: data.prescription && data.prescription.trim() ? {
          medications: [{
            name: data.prescription.trim(),
            dosage: '',
            frequency: '',
            duration: '',
            instructions: ''
          }],
          therapies: [],
          lifestyle: []
        } : undefined,
        notes: data.notes,
        followUpDate: data.followUpDate
      };

      const response = await consultationsAPI.update(selectedConsultation._id, consultationData);
      if (response.success) {
        toast.success('Consultation updated successfully');
        setIsEditDialogOpen(false);
        setSelectedConsultation(null);
        fetchData();
      } else {
        toast.error(response.message || 'Failed to update consultation');
      }
    } catch (error: any) {
      console.error('Error updating consultation:', error);
      const errorMessage = error?.response?.data?.message || error?.message || 'Failed to update consultation';
      toast.error(errorMessage);
    } finally {
      setFormLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: { [key: string]: "default" | "secondary" | "destructive" | "outline" } = {
      'Completed': "default",
      'In Progress': "secondary",
      'Scheduled': "outline",
      'Cancelled': "destructive",
      'No Show': "destructive"
    };
    const variant = variants[status] || "outline";
    return <Badge variant={variant}>{status}</Badge>;
  };

  const getModeIcon = (mode: string) => {
    return mode === "video" ? <Video className="h-4 w-4" /> : <Phone className="h-4 w-4" />;
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold">Consultations</h1>
          <p className="text-muted-foreground">Manage all consultation bookings</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleRefresh} variant="outline" disabled={refreshing}>
            <RefreshCw className={`mr-2 h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Schedule New
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-7xl max-w-[90vw] max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Create New Consultation</DialogTitle>
              </DialogHeader>
              <ConsultationForm
                onSubmit={handleCreateConsultation}
                onCancel={() => setIsCreateDialogOpen(false)}
                isLoading={formLoading}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search patients, doctors..."
                  className="pl-9"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="scheduled">Scheduled</SelectItem>
                <SelectItem value="in progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
                <SelectItem value="no show">No Show</SelectItem>
              </SelectContent>
            </Select>
            <Select value={doctorFilter} onValueChange={setDoctorFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Doctor" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Doctors</SelectItem>
                {doctors.map((doctor) => (
                  <SelectItem key={doctor._id} value={doctor._id}>
                    {doctor.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              More Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Consultations Table */}
      <Card>
        <CardHeader>
          <CardTitle>
            All Consultations ({filteredConsultations.length})
            {loading && <span className="text-sm font-normal text-muted-foreground ml-2">Loading...</span>}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading consultations...</span>
            </div>
          ) : filteredConsultations.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No consultations found matching your criteria.
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-2">ID</th>
                    <th className="text-left py-3 px-2">Patient</th>
                    <th className="text-left py-3 px-2">Doctor</th>
                    <th className="text-left py-3 px-2">Date & Time</th>
                    <th className="text-left py-3 px-2">Type</th>
                    <th className="text-left py-3 px-2">Mode</th>
                    <th className="text-left py-3 px-2">Fee</th>
                    <th className="text-left py-3 px-2">Status</th>
                    <th className="text-left py-3 px-2">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredConsultations.map((consultation) => (
                    <tr key={consultation._id} className="border-b hover:bg-muted/50">
                      <td className="py-3 px-2 font-mono text-sm">
                        {consultation._id.slice(-6).toUpperCase()}
                      </td>
                      <td className="py-3 px-2">
                        <div>
                          <div className="font-medium">{consultation.patient.name}</div>
                          <div className="text-sm text-muted-foreground">{consultation.patient.phone}</div>
                        </div>
                      </td>
                      <td className="py-3 px-2">
                        <div>
                          <div className="font-medium">{consultation.doctor.name}</div>
                          <div className="text-sm text-muted-foreground">{consultation.doctor.specialization}</div>
                        </div>
                      </td>
                      <td className="py-3 px-2">
                        <div>
                          <div>{new Date(consultation.appointmentDate).toLocaleDateString()}</div>
                          <div className="text-sm text-muted-foreground">{consultation.appointmentTime}</div>
                        </div>
                      </td>
                      <td className="py-3 px-2">{consultation.type}</td>
                      <td className="py-3 px-2">
                        <div className="flex items-center space-x-2">
                          {getModeIcon(consultation.mode)}
                          <span className="capitalize">{consultation.mode}</span>
                        </div>
                      </td>
                      <td className="py-3 px-2">
                        {consultation.fee ? `₹${consultation.fee.amount}` : 'N/A'}
                      </td>
                      <td className="py-3 px-2">{getStatusBadge(consultation.status)}</td>
                      <td className="py-3 px-2">
                        <div className="flex space-x-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleViewConsultation(consultation)}
                            title="View Details"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleEditConsultation(consultation)}
                            title="Edit Consultation"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Popover
                            open={deletePopoverOpen === consultation._id}
                            onOpenChange={(open) => setDeletePopoverOpen(open ? consultation._id : null)}
                          >
                            <PopoverTrigger asChild>
                              <Button
                                size="sm"
                                variant="ghost"
                                title="Delete Consultation"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-80" align="end">
                              <div className="space-y-4">
                                <div className="flex items-start space-x-3">
                                  <div className="flex-shrink-0">
                                    <AlertTriangle className="h-5 w-5 text-red-500" />
                                  </div>
                                  <div className="flex-1">
                                    <h3 className="text-sm font-medium text-gray-900">
                                      Delete Consultation
                                    </h3>
                                    <p className="text-sm text-gray-500 mt-1">
                                      Are you sure you want to delete this consultation? This action cannot be undone.
                                    </p>
                                  </div>
                                </div>

                                <div className="bg-gray-50 rounded-md p-3 space-y-2">
                                  <div className="text-xs text-gray-600">
                                    <strong>Patient:</strong> {consultation.patient.name}
                                  </div>
                                  <div className="text-xs text-gray-600">
                                    <strong>Doctor:</strong> {consultation.doctor.name}
                                  </div>
                                  <div className="text-xs text-gray-600">
                                    <strong>Date:</strong> {new Date(consultation.appointmentDate).toLocaleDateString()}
                                  </div>
                                  <div className="text-xs text-gray-600">
                                    <strong>Time:</strong> {consultation.appointmentTime}
                                  </div>
                                </div>

                                <div className="flex justify-end space-x-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setDeletePopoverOpen(null)}
                                    disabled={deleteLoading}
                                  >
                                    Cancel
                                  </Button>
                                  <Button
                                    variant="destructive"
                                    size="sm"
                                    onClick={() => {
                                      const consultationId = consultation._id;
                                      console.log('Delete button clicked for consultation ID:', consultationId, 'Type:', typeof consultationId);
                                      handleDeleteConsultation(String(consultationId));
                                    }}
                                    disabled={deleteLoading}
                                  >
                                    {deleteLoading ? (
                                      <>
                                        <RefreshCw className="mr-2 h-3 w-3 animate-spin" />
                                        Deleting...
                                      </>
                                    ) : (
                                      <>
                                        <Trash2 className="mr-2 h-3 w-3" />
                                        Delete
                                      </>
                                    )}
                                  </Button>
                                </div>
                              </div>
                            </PopoverContent>
                          </Popover>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* View Consultation Dialog */}
      {selectedConsultation && (
        <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Consultation Details</DialogTitle>
            </DialogHeader>
            <div className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Consultation ID</label>
                  <p className="text-sm font-mono">{selectedConsultation._id.slice(-8).toUpperCase()}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Status</label>
                  <p className="text-sm">{getStatusBadge(selectedConsultation.status)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Patient</label>
                  <p className="text-sm">{selectedConsultation.patient.name}</p>
                  <p className="text-xs text-gray-500">{selectedConsultation.patient.phone}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Doctor</label>
                  <p className="text-sm">{selectedConsultation.doctor.name}</p>
                  <p className="text-xs text-gray-500">{selectedConsultation.doctor.specialization}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Date & Time</label>
                  <p className="text-sm">{new Date(selectedConsultation.appointmentDate).toLocaleDateString()}</p>
                  <p className="text-xs text-gray-500">{selectedConsultation.appointmentTime}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Type & Mode</label>
                  <p className="text-sm">{selectedConsultation.type}</p>
                  <p className="text-xs text-gray-500">{selectedConsultation.mode}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Fee</label>
                  <p className="text-sm">₹{selectedConsultation.fee?.amount || 'N/A'}</p>
                </div>
              </div>

              {/* Medical Details */}
              {(selectedConsultation.symptoms || selectedConsultation.diagnosis || selectedConsultation.prescription || selectedConsultation.notes) && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Medical Details</h3>
                  {selectedConsultation.symptoms && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Symptoms</label>
                      <p className="text-sm mt-1">{selectedConsultation.symptoms}</p>
                    </div>
                  )}
                  {selectedConsultation.diagnosis && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Diagnosis</label>
                      <p className="text-sm mt-1">{selectedConsultation.diagnosis}</p>
                    </div>
                  )}
                  {selectedConsultation.prescription && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Prescription</label>
                      <p className="text-sm mt-1">{selectedConsultation.prescription}</p>
                    </div>
                  )}
                  {selectedConsultation.notes && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Notes</label>
                      <p className="text-sm mt-1">{selectedConsultation.notes}</p>
                    </div>
                  )}
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-end space-x-2 pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsViewDialogOpen(false);
                    setSelectedConsultation(null);
                  }}
                >
                  Close
                </Button>
                <Button
                  onClick={() => {
                    setIsViewDialogOpen(false);
                    setIsEditDialogOpen(true);
                  }}
                >
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Edit Consultation Dialog */}
      {selectedConsultation && (
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Consultation</DialogTitle>
            </DialogHeader>
            <ConsultationForm
              consultation={selectedConsultation}
              onSubmit={handleUpdateConsultation}
              onCancel={() => {
                setIsEditDialogOpen(false);
                setSelectedConsultation(null);
              }}
              isLoading={formLoading}
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}